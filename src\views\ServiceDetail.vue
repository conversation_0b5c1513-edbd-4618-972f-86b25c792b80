<template>
  <div class="service-detail-container">
    <div v-if="!currentService" class="no-service">
      <el-result
        icon="warning"
        title="服务未找到"
        sub-title="请从首页选择一个服务进入"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/home')">
            返回首页
          </el-button>
        </template>
      </el-result>
    </div>

    <div v-else class="service-content">
      <!-- 服务头部信息 -->
      <div class="service-header">
        <el-card>
          <div class="header-content">
            <div class="service-info">
              <div class="service-icon" :style="{ backgroundColor: currentService.color }">
                <el-icon size="32">
                  <component :is="currentService.icon" />
                </el-icon>
              </div>
              <div class="service-details">
                <h1>{{ currentService.name }}</h1>
                <p>{{ currentService.description }}</p>
                <div class="service-meta">
                  <el-tag type="info" size="small">{{ currentService.url }}</el-tag>
                  <el-tag type="success" size="small">端口: {{ currentService.port }}</el-tag>
                  <el-tag type="warning" size="small">{{ currentService.features.length }} 个功能</el-tag>
                </div>
              </div>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="openServiceUrl">
                <el-icon><Link /></el-icon>
                直接访问
              </el-button>
              <el-button @click="checkHealth">
                <el-icon><CircleCheck /></el-icon>
                健康检查
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 功能模块网格 -->
      <div class="features-section">
        <h2 class="section-title">功能模块</h2>
        <el-row :gutter="20">
          <el-col 
            v-for="feature in currentService.features" 
            :key="feature.id"
            :xs="24" 
            :sm="12" 
            :md="8" 
            :lg="6"
          >
            <el-card 
              class="feature-card" 
              shadow="hover"
              @click="navigateToFeature(feature)"
            >
              <div class="feature-content">
                <div class="feature-icon">
                  <el-icon size="24" :color="currentService.color">
                    <component :is="feature.icon" />
                  </el-icon>
                </div>
                <h3>{{ feature.name }}</h3>
                <p v-if="feature.description">{{ feature.description }}</p>
                <div class="feature-actions">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click.stop="navigateToFeature(feature)"
                  >
                    进入功能
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 服务统计 -->
      <div class="stats-section">
        <h2 class="section-title">服务统计</h2>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <el-statistic title="功能模块" :value="currentService.features.length" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <el-statistic title="服务状态" value="正常" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <el-statistic title="响应时间" value="120" suffix="ms" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <el-statistic title="今日访问" :value="Math.floor(Math.random() * 1000)" />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/stores/app'
import { getServiceById } from '@/config/services'
import type { ServiceFeature } from '@/config/services'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// 获取当前服务
const currentService = computed(() => {
  const serviceId = route.params.serviceId as string
  return getServiceById(serviceId)
})

// 打开服务URL
const openServiceUrl = () => {
  if (currentService.value) {
    window.open(currentService.value.url, '_blank')
    ElMessage.success(`正在打开 ${currentService.value.name}`)
  }
}

// 健康检查
const checkHealth = async () => {
  if (!currentService.value) return
  
  try {
    ElMessage.info(`正在检查 ${currentService.value.name} 的健康状态...`)
    // 模拟健康检查
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success(`${currentService.value.name} 运行正常`)
  } catch (error) {
    ElMessage.error(`${currentService.value.name} 健康检查失败`)
  }
}

// 导航到功能页面
const navigateToFeature = (feature: ServiceFeature) => {
  if (currentService.value) {
    router.push(`/service/${currentService.value.id}/${feature.id}`)
  }
}
</script>

<style scoped>
.service-detail-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.no-service {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.service-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.service-details h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.service-details p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.service-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.section-title {
  font-size: 20px;
  margin-bottom: 20px;
  color: #303133;
}

.features-section {
  margin-bottom: 30px;
}

.feature-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-content {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  margin-bottom: 12px;
}

.feature-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.feature-content p {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 12px;
}

.feature-actions {
  margin-top: 16px;
}

.stats-section {
  margin-bottom: 30px;
}

.stat-card {
  text-align: center;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .service-info {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
