{"name": "spider-admin", "version": "1.0.0", "description": "多服务管理平台", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "type-check": "vue-tsc --noEmit", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "deploy": "node scripts/deploy.js"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "typescript": "^5.2.2", "vue-tsc": "^1.8.25", "@types/node": "^20.10.5", "@types/js-cookie": "^3.0.6", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "vitest": "^1.1.0", "@vitest/ui": "^1.1.0"}}