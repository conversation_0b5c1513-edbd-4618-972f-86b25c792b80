/* CSS 变量定义 */
:root {
  /* 主色调 */
  --primary-color: #409EFF;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;

  /* 辅助色 */
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;

  /* 中性色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;

  /* 边框色 */
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;

  /* 背景色 */
  --bg-color: #FFFFFF;
  --bg-page: #F5F5F5;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 阴影 */
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);

  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  --border-radius-round: 20px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 字体 */
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;

  /* 行高 */
  --line-height-base: 1.5;
  --line-height-sm: 1.25;
  --line-height-lg: 1.75;

  /* 过渡动画 */
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-slow: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);

  /* 层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* 服务主题色 */
  --service1-color: #409EFF;
  --service2-color: #67C23A;
  --service3-color: #E6A23C;

  /* 侧边栏 */
  --sidebar-bg: #304156;
  --sidebar-text: #bfcbd9;
  --sidebar-active: #409EFF;
  --sidebar-hover: #434a50;

  /* 头部导航 */
  --header-bg: #FFFFFF;
  --header-border: #E4E7ED;
  --header-height: 60px;

  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 992px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1920px;
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --bg-page: #121212;
  --text-primary: #ffffff;
  --text-regular: #e0e0e0;
  --text-secondary: #a0a0a0;
  --border-base: #404040;
  --border-light: #505050;
  --sidebar-bg: #1f1f1f;
  --header-bg: #2a2a2a;
}
