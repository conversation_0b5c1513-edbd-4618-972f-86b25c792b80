<template>
  <div class="user-management-demo">
    <div class="demo-header">
      <h2>用户管理演示</h2>
      <p>这是用户管理服务的功能演示页面</p>
    </div>

    <!-- 用户列表 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <el-button type="primary" size="small" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加用户
          </el-button>
        </div>
      </template>

      <el-table :data="users" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag :type="getRoleType(scope.row.role)">
              {{ scope.row.role }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '活跃' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteUser(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加用户对话框 -->
    <el-dialog v-model="showAddDialog" title="添加用户" width="500px">
      <el-form :model="newUser" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="newUser.name" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="newUser.email" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="newUser.role" style="width: 100%">
            <el-option label="管理员" value="管理员" />
            <el-option label="用户" value="用户" />
            <el-option label="访客" value="访客" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="addUser">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface User {
  id: number
  name: string
  email: string
  role: string
  status: string
  createTime: string
}

const showAddDialog = ref(false)
const newUser = ref({
  name: '',
  email: '',
  role: '用户'
})

const users = ref<User[]>([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    role: '管理员',
    status: '活跃',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    role: '用户',
    status: '活跃',
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    role: '访客',
    status: '禁用',
    createTime: '2024-01-17 09:15:00'
  }
])

const getRoleType = (role: string) => {
  switch (role) {
    case '管理员': return 'danger'
    case '用户': return 'primary'
    case '访客': return 'info'
    default: return 'info'
  }
}

const addUser = () => {
  if (!newUser.value.name || !newUser.value.email) {
    ElMessage.error('请填写完整信息')
    return
  }

  const user: User = {
    id: users.value.length + 1,
    name: newUser.value.name,
    email: newUser.value.email,
    role: newUser.value.role,
    status: '活跃',
    createTime: new Date().toLocaleString()
  }

  users.value.push(user)
  showAddDialog.value = false
  newUser.value = { name: '', email: '', role: '用户' }
  ElMessage.success('用户添加成功')
}

const editUser = (user: User) => {
  ElMessage.info(`编辑用户: ${user.name}`)
}

const deleteUser = (user: User) => {
  ElMessageBox.confirm(`确定要删除用户 ${user.name} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  })
}
</script>

<style scoped>
.user-management-demo {
  padding: 20px;
}

.demo-header {
  margin-bottom: 20px;
  text-align: center;
}

.demo-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.demo-header p {
  color: #606266;
  margin: 0;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
