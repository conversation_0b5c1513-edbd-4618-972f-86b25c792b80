<template>
  <div class="service-feature-container">
    <div v-if="!currentService || !currentFeature" class="no-feature">
      <el-result
        icon="warning"
        title="功能未找到"
        sub-title="请从服务页面选择一个功能进入"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/home')">
            返回首页
          </el-button>
        </template>
      </el-result>
    </div>

    <div v-else class="feature-content">
      <!-- 功能头部 -->
      <div class="feature-header">
        <el-card>
          <div class="header-content">
            <div class="feature-info">
              <div class="feature-icon" :style="{ backgroundColor: currentService.color }">
                <el-icon size="24">
                  <component :is="currentFeature.icon" />
                </el-icon>
              </div>
              <div class="feature-details">
                <h1>{{ currentFeature.name }}</h1>
                <p v-if="currentFeature.description">{{ currentFeature.description }}</p>
                <div class="breadcrumb">
                  <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
                    <el-breadcrumb-item :to="{ path: `/service/${currentService.id}` }">
                      {{ currentService.name }}
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>{{ currentFeature.name }}</el-breadcrumb-item>
                  </el-breadcrumb>
                </div>
              </div>
            </div>
            <div class="header-actions">
              <el-button @click="goBack">
                <el-icon><ArrowLeft /></el-icon>
                返回服务
              </el-button>
              <el-button type="primary" @click="openInNewTab">
                <el-icon><Link /></el-icon>
                新窗口打开
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 功能内容区域 -->
      <div class="feature-main">
        <el-card>
          <div class="iframe-container" v-if="showIframe">
            <iframe 
              :src="featureUrl" 
              frameborder="0"
              class="feature-iframe"
              @load="onIframeLoad"
            ></iframe>
          </div>
          
          <!-- 功能演示内容 -->
          <div v-else class="demo-content">
            <component :is="getDemoComponent()" />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getServiceById } from '@/config/services'

// 导入演示组件
import UserManagementDemo from '@/components/demos/UserManagementDemo.vue'
import DataAnalysisDemo from '@/components/demos/DataAnalysisDemo.vue'
import SystemMonitorDemo from '@/components/demos/SystemMonitorDemo.vue'

const route = useRoute()
const router = useRouter()

const showIframe = ref(false) // 是否显示iframe

// 获取当前服务和功能
const currentService = computed(() => {
  const serviceId = route.params.serviceId as string
  return getServiceById(serviceId)
})

const currentFeature = computed(() => {
  const featureId = route.params.featureId as string
  if (!currentService.value || !featureId) return null
  
  return currentService.value.features.find(f => f.id === featureId)
})

// 功能URL
const featureUrl = computed(() => {
  if (!currentService.value || !currentFeature.value) return ''
  return `${currentService.value.url}${currentFeature.value.path}`
})

// 返回服务页面
const goBack = () => {
  if (currentService.value) {
    router.push(`/service/${currentService.value.id}`)
  }
}

// 新窗口打开
const openInNewTab = () => {
  if (featureUrl.value) {
    window.open(featureUrl.value, '_blank')
    ElMessage.success('已在新窗口打开功能页面')
  }
}

// iframe加载完成
const onIframeLoad = () => {
  ElMessage.success('功能页面加载完成')
}

// 获取演示组件
const getDemoComponent = () => {
  if (!currentService.value || !currentFeature.value) return null
  
  const serviceId = currentService.value.id
  const featureId = currentFeature.value.id
  
  // 根据服务和功能返回对应的演示组件
  if (serviceId === 'service1') {
    return UserManagementDemo
  } else if (serviceId === 'service2') {
    return DataAnalysisDemo
  } else if (serviceId === 'service3') {
    return SystemMonitorDemo
  }
  
  return null
}
</script>

<style scoped>
.service-feature-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.no-feature {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.feature-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.feature-details h1 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.feature-details p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.breadcrumb {
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.feature-main {
  min-height: 600px;
}

.iframe-container {
  width: 100%;
  height: 600px;
  position: relative;
}

.feature-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.demo-content {
  min-height: 500px;
  padding: 20px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .feature-info {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .iframe-container {
    height: 400px;
  }
}
</style>
