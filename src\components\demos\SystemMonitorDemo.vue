<template>
  <div class="system-monitor-demo">
    <div class="demo-header">
      <h2>系统监控演示</h2>
      <p>这是系统监控服务的功能演示页面</p>
    </div>

    <!-- 系统状态概览 -->
    <el-row :gutter="20" class="status-row">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon cpu">
              <el-icon size="24"><Cpu /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">CPU使用率</div>
              <div class="status-value">{{ cpuUsage }}%</div>
              <el-progress :percentage="cpuUsage" :color="getProgressColor(cpuUsage)" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon memory">
              <el-icon size="24"><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">内存使用率</div>
              <div class="status-value">{{ memoryUsage }}%</div>
              <el-progress :percentage="memoryUsage" :color="getProgressColor(memoryUsage)" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon disk">
              <el-icon size="24"><FolderOpened /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">磁盘使用率</div>
              <div class="status-value">{{ diskUsage }}%</div>
              <el-progress :percentage="diskUsage" :color="getProgressColor(diskUsage)" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon network">
              <el-icon size="24"><Connection /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">网络流量</div>
              <div class="status-value">{{ networkSpeed }} MB/s</div>
              <div class="network-info">
                <span class="upload">↑ {{ uploadSpeed }} MB/s</span>
                <span class="download">↓ {{ downloadSpeed }} MB/s</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>服务状态</span>
          </template>
          <div class="service-list">
            <div 
              v-for="service in services" 
              :key="service.name"
              class="service-item"
            >
              <div class="service-info">
                <div class="service-name">{{ service.name }}</div>
                <div class="service-port">端口: {{ service.port }}</div>
              </div>
              <div class="service-status">
                <el-tag 
                  :type="service.status === 'running' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ service.status === 'running' ? '运行中' : '已停止' }}
                </el-tag>
                <div class="service-uptime">
                  运行时间: {{ service.uptime }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统日志</span>
          </template>
          <div class="log-container">
            <div 
              v-for="log in logs" 
              :key="log.id"
              class="log-item"
              :class="log.level"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-level">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 告警信息 -->
    <el-card class="alerts-card">
      <template #header>
        <div class="card-header">
          <span>系统告警</span>
          <el-button type="primary" size="small">
            <el-icon><Bell /></el-icon>
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table :data="alerts" style="width: 100%">
        <el-table-column prop="level" label="级别" width="100">
          <template #default="scope">
            <el-tag :type="getAlertType(scope.row.level)">
              {{ scope.row.level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="告警标题" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="handleAlert(scope.row)">
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

interface Service {
  name: string
  port: number
  status: 'running' | 'stopped'
  uptime: string
}

interface LogItem {
  id: number
  time: string
  level: 'info' | 'warn' | 'error'
  message: string
}

interface Alert {
  id: number
  level: '严重' | '警告' | '信息'
  title: string
  description: string
  time: string
}

const cpuUsage = ref(45)
const memoryUsage = ref(68)
const diskUsage = ref(32)
const networkSpeed = ref(12.5)
const uploadSpeed = ref(5.2)
const downloadSpeed = ref(7.3)

const services = ref<Service[]>([
  { name: '用户管理服务', port: 8081, status: 'running', uptime: '15天 8小时' },
  { name: '数据分析服务', port: 8082, status: 'running', uptime: '12天 3小时' },
  { name: '系统监控服务', port: 8083, status: 'running', uptime: '20天 15小时' },
  { name: 'Redis缓存', port: 6379, status: 'running', uptime: '25天 2小时' },
  { name: 'MySQL数据库', port: 3306, status: 'running', uptime: '30天 10小时' }
])

const logs = ref<LogItem[]>([
  { id: 1, time: '2024-01-20 14:30:25', level: 'info', message: '用户登录成功: <EMAIL>' },
  { id: 2, time: '2024-01-20 14:28:15', level: 'warn', message: 'CPU使用率超过70%' },
  { id: 3, time: '2024-01-20 14:25:10', level: 'error', message: '数据库连接超时' },
  { id: 4, time: '2024-01-20 14:20:05', level: 'info', message: '系统备份完成' },
  { id: 5, time: '2024-01-20 14:15:30', level: 'info', message: '定时任务执行成功' }
])

const alerts = ref<Alert[]>([
  { id: 1, level: '警告', title: 'CPU使用率过高', description: 'CPU使用率持续超过80%', time: '2024-01-20 14:30:00' },
  { id: 2, level: '信息', title: '系统更新可用', description: '有新的系统更新可以安装', time: '2024-01-20 12:15:00' },
  { id: 3, level: '严重', title: '磁盘空间不足', description: '系统磁盘剩余空间少于10%', time: '2024-01-20 10:45:00' }
])

let timer: NodeJS.Timeout | null = null

const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

const getAlertType = (level: string) => {
  switch (level) {
    case '严重': return 'danger'
    case '警告': return 'warning'
    case '信息': return 'info'
    default: return 'info'
  }
}

const handleAlert = (alert: Alert) => {
  ElMessage.success(`正在处理告警: ${alert.title}`)
}

const updateSystemStats = () => {
  // 模拟实时数据更新
  cpuUsage.value = Math.floor(Math.random() * 30) + 40
  memoryUsage.value = Math.floor(Math.random() * 20) + 60
  diskUsage.value = Math.floor(Math.random() * 15) + 25
  networkSpeed.value = Math.floor(Math.random() * 10) + 8
  uploadSpeed.value = Math.floor(Math.random() * 5) + 3
  downloadSpeed.value = Math.floor(Math.random() * 8) + 5
}

onMounted(() => {
  timer = setInterval(updateSystemStats, 3000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.system-monitor-demo {
  padding: 20px;
}

.demo-header {
  margin-bottom: 20px;
  text-align: center;
}

.demo-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.demo-header p {
  color: #606266;
  margin: 0;
}

.status-row {
  margin-bottom: 20px;
}

.status-card {
  height: 120px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 100%;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.status-icon.cpu { background: #409EFF; }
.status-icon.memory { background: #67C23A; }
.status-icon.disk { background: #E6A23C; }
.status-icon.network { background: #F56C6C; }

.status-info {
  flex: 1;
}

.status-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.network-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  margin-top: 4px;
}

.upload { color: #F56C6C; }
.download { color: #67C23A; }

.service-list {
  max-height: 300px;
  overflow-y: auto;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-name {
  font-weight: 500;
  color: #303133;
}

.service-port {
  font-size: 12px;
  color: #909399;
}

.service-status {
  text-align: right;
}

.service-uptime {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
}

.log-item {
  display: flex;
  gap: 8px;
  padding: 4px 0;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #909399;
  min-width: 120px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-item.info .log-level { color: #409EFF; }
.log-item.warn .log-level { color: #E6A23C; }
.log-item.error .log-level { color: #F56C6C; }

.log-message {
  flex: 1;
  color: #303133;
}

.alerts-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
