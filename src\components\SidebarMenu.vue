<template>
  <div class="sidebar-menu">
    <!-- Logo区域 -->
    <div class="logo-area">
      <div class="logo" v-if="!appStore.isCollapsed">
        <el-icon><Monitor /></el-icon>
        <span>{{ appTitle }}</span>
      </div>
      <div class="logo-mini" v-else>
        <el-icon><Monitor /></el-icon>
      </div>
    </div>

    <!-- 菜单区域 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.isCollapsed"
      :unique-opened="true"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
    >
      <!-- 首页 -->
      <el-menu-item index="/home">
        <el-icon><HomeFilled /></el-icon>
        <template #title>首页</template>
      </el-menu-item>

      <!-- 服务菜单 -->
      <el-sub-menu 
        v-for="service in appStore.services" 
        :key="service.id"
        :index="service.id"
      >
        <template #title>
          <el-icon>
            <component :is="service.icon" />
          </el-icon>
          <span>{{ service.name }}</span>
        </template>
        
        <el-menu-item 
          v-for="feature in service.features"
          :key="feature.id"
          :index="`/service/${service.id}/${feature.id}`"
        >
          <el-icon>
            <component :is="feature.icon" />
          </el-icon>
          <template #title>{{ feature.name }}</template>
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const appStore = useAppStore()

const appTitle = import.meta.env.VITE_APP_TITLE || 'Spider Admin'

const activeMenu = computed(() => {
  return route.path
})
</script>

<style scoped>
.sidebar-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-area {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #434a50;
  background: #2b3441;
}

.logo {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.logo .el-icon {
  margin-right: 8px;
  font-size: 24px;
}

.logo-mini {
  color: #fff;
  font-size: 24px;
}

.el-menu {
  border: none;
  flex: 1;
}

.el-menu:not(.el-menu--collapse) {
  width: 200px;
}
</style>
