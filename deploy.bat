@echo off
setlocal enabledelayedexpansion

REM Spider Admin Windows 部署脚本
REM 使用方法: deploy.bat [environment]
REM 环境: development | production (默认: production)

set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=production

set PROJECT_NAME=spider-admin
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo.
echo ========================================
echo   Spider Admin 部署脚本 (Windows)
echo ========================================
echo 环境: %ENVIRONMENT%
echo 时间: %date% %time%
echo ========================================
echo.

REM 环境配置
if "%ENVIRONMENT%"=="development" (
    set BUILD_COMMAND=npm run build:dev
    set SERVER_PATH=C:\inetpub\wwwroot\spider-admin-dev
    set BACKUP_PATH=C:\backups\spider-admin-dev
) else if "%ENVIRONMENT%"=="production" (
    set BUILD_COMMAND=npm run build
    set SERVER_PATH=C:\inetpub\wwwroot\spider-admin
    set BACKUP_PATH=C:\backups\spider-admin
) else (
    echo 错误: 未知环境 %ENVIRONMENT%
    echo 支持的环境: development, production
    exit /b 1
)

echo 配置信息:
echo   构建命令: %BUILD_COMMAND%
echo   服务器路径: %SERVER_PATH%
echo   备份路径: %BACKUP_PATH%
echo.

REM 检查 Node.js
echo [1/8] 检查 Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Node.js 未安装或不在 PATH 中
    exit /b 1
)
echo ✓ Node.js 检查通过

REM 检查 npm
echo [2/8] 检查 npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo 错误: npm 未安装或不在 PATH 中
    exit /b 1
)
echo ✓ npm 检查通过

REM 创建备份
echo [3/8] 创建备份...
if exist "%SERVER_PATH%" (
    if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"
    set BACKUP_FILE=%BACKUP_PATH%\backup_%TIMESTAMP%.zip
    powershell -Command "Compress-Archive -Path '%SERVER_PATH%' -DestinationPath '!BACKUP_FILE!' -Force"
    if errorlevel 1 (
        echo 警告: 备份创建失败
    ) else (
        echo ✓ 备份已创建: !BACKUP_FILE!
    )
) else (
    echo ℹ 服务器路径不存在，跳过备份
)

REM 安装依赖
echo [4/8] 安装依赖...
call npm ci
if errorlevel 1 (
    echo 错误: 依赖安装失败
    exit /b 1
)
echo ✓ 依赖安装完成

REM 运行测试
echo [5/8] 运行测试...
call npm test >nul 2>&1
if errorlevel 1 (
    echo 警告: 测试失败或未配置测试
    set /p CONTINUE="是否继续部署? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo 部署已取消
        exit /b 1
    )
) else (
    echo ✓ 测试通过
)

REM 构建项目
echo [6/8] 构建项目...
if exist "dist" rmdir /s /q "dist"
call %BUILD_COMMAND%
if errorlevel 1 (
    echo 错误: 项目构建失败
    exit /b 1
)
if not exist "dist" (
    echo 错误: 构建输出目录不存在
    exit /b 1
)
echo ✓ 项目构建完成

REM 部署文件
echo [7/8] 部署文件...
if not exist "%SERVER_PATH%" mkdir "%SERVER_PATH%"
if exist "%SERVER_PATH%\*" del /q "%SERVER_PATH%\*"
if exist "%SERVER_PATH%\assets" rmdir /s /q "%SERVER_PATH%\assets"
xcopy "dist\*" "%SERVER_PATH%\" /s /e /y >nul
if errorlevel 1 (
    echo 错误: 文件部署失败
    exit /b 1
)
echo ✓ 文件部署完成

REM 健康检查
echo [8/8] 健康检查...
if not exist "%SERVER_PATH%\index.html" (
    echo 错误: index.html 文件不存在
    exit /b 1
)
echo ✓ 健康检查通过

REM 显示部署信息
echo.
echo ========================================
echo           🎉 部署完成！
echo ========================================
echo 环境: %ENVIRONMENT%
echo 路径: %SERVER_PATH%
echo 时间: %date% %time%
if exist "!BACKUP_FILE!" echo 备份: !BACKUP_FILE!
echo.
echo 🌐 访问地址:
echo   本地: file:///%SERVER_PATH:\=/%/index.html
echo   IIS: http://localhost/spider-admin
echo.
echo 📝 后续步骤:
echo   1. 配置 IIS 站点指向 %SERVER_PATH%
echo   2. 确保 IIS 支持 SPA 路由重写
echo   3. 配置 HTTPS 证书 (生产环境)
echo ========================================

endlocal
pause
