// 服务配置管理
export interface ServiceConfig {
  id: string
  name: string
  description: string
  url: string
  port: number
  icon: string
  color: string
  features: ServiceFeature[]
}

export interface ServiceFeature {
  id: string
  name: string
  path: string
  icon: string
  description?: string
}

// 获取环境变量
const getEnvVar = (key: string, defaultValue: string = '') => {
  return import.meta.env[key] || defaultValue
}

// 服务配置
export const SERVICES: ServiceConfig[] = [
  {
    id: 'service1',
    name: getEnvVar('VITE_SERVICE1_NAME', '用户管理服务'),
    description: '用户账户管理、权限控制、角色分配',
    url: getEnvVar('VITE_SERVICE1_URL', 'http://localhost:8081'),
    port: 8081,
    icon: 'User',
    color: '#409EFF',
    features: [
      { id: 'users', name: '用户管理', path: '/users', icon: 'UserFilled' },
      { id: 'roles', name: '角色管理', path: '/roles', icon: 'Avatar' },
      { id: 'permissions', name: '权限管理', path: '/permissions', icon: 'Key' },
      { id: 'departments', name: '部门管理', path: '/departments', icon: 'OfficeBuilding' },
      { id: 'audit', name: '操作审计', path: '/audit', icon: 'Document' }
    ]
  },
  {
    id: 'service2',
    name: getEnvVar('VITE_SERVICE2_NAME', '数据分析服务'),
    description: '数据统计、报表生成、趋势分析',
    url: getEnvVar('VITE_SERVICE2_URL', 'http://localhost:8082'),
    port: 8082,
    icon: 'DataAnalysis',
    color: '#67C23A',
    features: [
      { id: 'dashboard', name: '数据看板', path: '/dashboard', icon: 'Monitor' },
      { id: 'reports', name: '报表管理', path: '/reports', icon: 'Document' },
      { id: 'charts', name: '图表分析', path: '/charts', icon: 'TrendCharts' },
      { id: 'export', name: '数据导出', path: '/export', icon: 'Download' },
      { id: 'schedule', name: '定时任务', path: '/schedule', icon: 'Timer' }
    ]
  },
  {
    id: 'service3',
    name: getEnvVar('VITE_SERVICE3_NAME', '系统监控服务'),
    description: '系统状态监控、性能分析、告警管理',
    url: getEnvVar('VITE_SERVICE3_URL', 'http://localhost:8083'),
    port: 8083,
    icon: 'Monitor',
    color: '#E6A23C',
    features: [
      { id: 'system', name: '系统监控', path: '/system', icon: 'Monitor' },
      { id: 'performance', name: '性能分析', path: '/performance', icon: 'TrendCharts' },
      { id: 'logs', name: '日志管理', path: '/logs', icon: 'Document' },
      { id: 'alerts', name: '告警管理', path: '/alerts', icon: 'Bell' },
      { id: 'health', name: '健康检查', path: '/health', icon: 'CircleCheck' }
    ]
  }
]

// 根据ID获取服务配置
export const getServiceById = (id: string): ServiceConfig | undefined => {
  return SERVICES.find(service => service.id === id)
}

// 获取所有服务
export const getAllServices = (): ServiceConfig[] => {
  return SERVICES
}

// 添加新服务的辅助函数
export const addService = (service: ServiceConfig): void => {
  SERVICES.push(service)
}
