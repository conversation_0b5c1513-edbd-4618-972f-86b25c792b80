<template>
  <div class="data-analysis-demo">
    <div class="demo-header">
      <h2>数据分析演示</h2>
      <p>这是数据分析服务的功能演示页面</p>
    </div>

    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="总用户数" :value="12580" />
          <div class="stat-trend">
            <el-icon color="#67C23A"><TrendCharts /></el-icon>
            <span class="trend-text success">+12.5%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="今日访问" :value="3456" />
          <div class="stat-trend">
            <el-icon color="#E6A23C"><TrendCharts /></el-icon>
            <span class="trend-text warning">-2.3%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="转化率" :value="68.5" suffix="%" />
          <div class="stat-trend">
            <el-icon color="#67C23A"><TrendCharts /></el-icon>
            <span class="trend-text success">+5.2%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="收入" :value="89650" prefix="¥" />
          <div class="stat-trend">
            <el-icon color="#67C23A"><TrendCharts /></el-icon>
            <span class="trend-text success">+18.7%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>访问趋势</span>
          </template>
          <div class="chart-container">
            <div class="mock-chart">
              <div class="chart-title">过去7天访问量</div>
              <div class="chart-bars">
                <div class="bar" style="height: 60%"></div>
                <div class="bar" style="height: 80%"></div>
                <div class="bar" style="height: 45%"></div>
                <div class="bar" style="height: 90%"></div>
                <div class="bar" style="height: 70%"></div>
                <div class="bar" style="height: 85%"></div>
                <div class="bar" style="height: 95%"></div>
              </div>
              <div class="chart-labels">
                <span>周一</span>
                <span>周二</span>
                <span>周三</span>
                <span>周四</span>
                <span>周五</span>
                <span>周六</span>
                <span>周日</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户分布</span>
          </template>
          <div class="chart-container">
            <div class="mock-pie-chart">
              <div class="pie-center">
                <div class="pie-title">用户类型</div>
                <div class="pie-total">12,580</div>
              </div>
              <div class="pie-legend">
                <div class="legend-item">
                  <div class="legend-color" style="background: #409EFF"></div>
                  <span>新用户 (45%)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color" style="background: #67C23A"></div>
                  <span>活跃用户 (35%)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color" style="background: #E6A23C"></div>
                  <span>沉睡用户 (20%)</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>热门页面</span>
          <el-button type="primary" size="small">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>
      
      <el-table :data="pageData" style="width: 100%">
        <el-table-column prop="page" label="页面" />
        <el-table-column prop="views" label="浏览量" sortable />
        <el-table-column prop="visitors" label="访客数" sortable />
        <el-table-column prop="bounceRate" label="跳出率" sortable>
          <template #default="scope">
            <span :class="getBounceRateClass(scope.row.bounceRate)">
              {{ scope.row.bounceRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="avgTime" label="平均停留时间" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface PageData {
  page: string
  views: number
  visitors: number
  bounceRate: number
  avgTime: string
}

const pageData = ref<PageData[]>([
  { page: '/home', views: 15420, visitors: 8930, bounceRate: 25.6, avgTime: '2:34' },
  { page: '/products', views: 12350, visitors: 7820, bounceRate: 32.1, avgTime: '3:12' },
  { page: '/about', views: 8760, visitors: 5640, bounceRate: 45.8, avgTime: '1:45' },
  { page: '/contact', views: 6540, visitors: 4320, bounceRate: 38.9, avgTime: '2:08' },
  { page: '/blog', views: 4230, visitors: 2890, bounceRate: 52.3, avgTime: '4:25' }
])

const getBounceRateClass = (rate: number) => {
  if (rate < 30) return 'bounce-rate-good'
  if (rate < 50) return 'bounce-rate-normal'
  return 'bounce-rate-high'
}
</script>

<style scoped>
.data-analysis-demo {
  padding: 20px;
}

.demo-header {
  margin-bottom: 20px;
  text-align: center;
}

.demo-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.demo-header p {
  color: #606266;
  margin: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  gap: 4px;
}

.trend-text {
  font-size: 12px;
  font-weight: bold;
}

.trend-text.success {
  color: #67C23A;
}

.trend-text.warning {
  color: #E6A23C;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mock-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 20px;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  height: 200px;
  margin-bottom: 10px;
}

.bar {
  width: 30px;
  background: linear-gradient(to top, #409EFF, #79bbff);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s;
}

.bar:hover {
  opacity: 0.8;
}

.chart-labels {
  display: flex;
  gap: 8px;
}

.chart-labels span {
  width: 30px;
  text-align: center;
  font-size: 12px;
  color: #909399;
}

.mock-pie-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.pie-center {
  text-align: center;
}

.pie-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.pie-total {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bounce-rate-good {
  color: #67C23A;
  font-weight: bold;
}

.bounce-rate-normal {
  color: #E6A23C;
  font-weight: bold;
}

.bounce-rate-high {
  color: #F56C6C;
  font-weight: bold;
}
</style>
