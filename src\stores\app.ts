import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ServiceConfig } from '@/config/services'
import { getAllServices } from '@/config/services'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isCollapsed = ref(false)
  const currentService = ref<ServiceConfig | null>(null)
  const services = ref<ServiceConfig[]>([])
  const loading = ref(false)

  // 计算属性
  const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '200px')
  
  // 操作
  const toggleSidebar = () => {
    isCollapsed.value = !isCollapsed.value
  }

  const setCurrentService = (service: ServiceConfig | null) => {
    currentService.value = service
  }

  const initApp = () => {
    services.value = getAllServices()
  }

  const setLoading = (status: boolean) => {
    loading.value = status
  }

  return {
    // 状态
    isCollapsed,
    currentService,
    services,
    loading,
    
    // 计算属性
    sidebarWidth,
    
    // 操作
    toggleSidebar,
    setCurrentService,
    initApp,
    setLoading
  }
})
