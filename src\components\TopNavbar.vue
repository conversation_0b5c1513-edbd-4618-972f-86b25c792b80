<template>
  <div class="top-navbar">
    <!-- 左侧 -->
    <div class="navbar-left">
      <el-button 
        type="text" 
        @click="appStore.toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon size="20">
          <Fold v-if="!appStore.isCollapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
      
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item v-if="currentService">
          {{ currentService.name }}
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="currentFeature">
          {{ currentFeature.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧 -->
    <div class="navbar-right">
      <!-- 环境标识 -->
      <el-tag 
        :type="envTagType" 
        size="small" 
        class="env-tag"
      >
        {{ envText }}
      </el-tag>
      
      <!-- 用户信息 -->
      <el-dropdown>
        <span class="user-info">
          <el-avatar size="small" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">管理员</span>
          <el-icon><ArrowDown /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>个人中心</el-dropdown-item>
            <el-dropdown-item>设置</el-dropdown-item>
            <el-dropdown-item divided>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { getServiceById } from '@/config/services'

const route = useRoute()
const appStore = useAppStore()

const userAvatar = ''

// 当前服务和功能
const currentService = computed(() => {
  const serviceId = route.params.serviceId as string
  return serviceId ? getServiceById(serviceId) : null
})

const currentFeature = computed(() => {
  const featureId = route.params.featureId as string
  if (!currentService.value || !featureId) return null
  
  return currentService.value.features.find(f => f.id === featureId)
})

// 环境标识
const envText = computed(() => {
  const mode = import.meta.env.MODE
  return mode === 'production' ? '生产环境' : '开发环境'
})

const envTagType = computed(() => {
  const mode = import.meta.env.MODE
  return mode === 'production' ? 'danger' : 'success'
})
</script>

<style scoped>
.top-navbar {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 20px;
  color: #606266;
}

.breadcrumb {
  font-size: 14px;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.env-tag {
  margin-right: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
}

.username {
  margin: 0 8px;
  font-size: 14px;
}
</style>
