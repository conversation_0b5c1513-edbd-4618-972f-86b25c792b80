<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Spider Admin - 多服务统一管理平台" />
    <meta name="keywords" content="管理平台,服务管理,Vue,Element Plus" />
    <meta name="author" content="Spider Admin Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Spider Admin" />
    <meta property="og:description" content="多服务统一管理平台" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Spider Admin" />
    <meta property="twitter:description" content="多服务统一管理平台" />
    
    <title>Spider Admin</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 样式预加载 -->
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e4e7ed;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #606266;
        font-size: 14px;
        margin-bottom: 10px;
      }
      
      .loading-progress {
        width: 200px;
        height: 4px;
        background: #e4e7ed;
        border-radius: 2px;
        overflow: hidden;
      }
      
      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #409eff, #79bbff);
        border-radius: 2px;
        animation: progress 2s ease-in-out infinite;
      }
      
      @keyframes progress {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
      }
      
      /* 隐藏加载动画 */
      .loading-hidden {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载 Spider Admin...</div>
      <div class="loading-progress">
        <div class="loading-progress-bar"></div>
      </div>
    </div>
    
    <!-- 应用根节点 -->
    <div id="app"></div>
    
    <!-- 不支持JavaScript的提示 -->
    <noscript>
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        text-align: center;
        padding: 20px;
      ">
        <h1 style="color: #303133; margin-bottom: 20px;">需要启用 JavaScript</h1>
        <p style="color: #606266; line-height: 1.6;">
          Spider Admin 需要 JavaScript 才能正常运行。<br>
          请在浏览器设置中启用 JavaScript，然后刷新页面。
        </p>
      </div>
    </noscript>
    
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 隐藏加载动画 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('loading-hidden');
            setTimeout(function() {
              loading.remove();
            }, 300);
          }
        }, 500);
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('应用加载错误:', e.error);
        const loading = document.getElementById('loading');
        if (loading) {
          loading.innerHTML = `
            <div style="text-align: center; color: #f56c6c;">
              <h3>加载失败</h3>
              <p>应用加载时发生错误，请刷新页面重试</p>
              <button onclick="location.reload()" style="
                padding: 8px 16px;
                background: #409eff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
              ">刷新页面</button>
            </div>
          `;
        }
      });
    </script>
  </body>
</html>
