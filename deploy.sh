#!/bin/bash

# Spider Admin 部署脚本
# 使用方法: ./deploy.sh [environment]
# 环境: development | production (默认: production)

set -e  # 遇到错误立即退出

# 配置
ENVIRONMENT=${1:-production}
PROJECT_NAME="spider-admin"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# 环境配置
case $ENVIRONMENT in
    "development")
        BUILD_COMMAND="npm run build:dev"
        SERVER_PATH="/var/www/spider-admin-dev"
        BACKUP_PATH="/var/backups/spider-admin-dev"
        NGINX_CONFIG="/etc/nginx/sites-available/spider-admin-dev"
        ;;
    "production")
        BUILD_COMMAND="npm run build"
        SERVER_PATH="/var/www/spider-admin"
        BACKUP_PATH="/var/backups/spider-admin"
        NGINX_CONFIG="/etc/nginx/sites-available/spider-admin"
        ;;
    *)
        error "未知环境: $ENVIRONMENT. 支持的环境: development, production"
        ;;
esac

# 检查权限
check_permissions() {
    log "检查部署权限..."
    
    if [ ! -w "$(dirname "$SERVER_PATH")" ]; then
        error "没有写入权限: $(dirname "$SERVER_PATH")"
    fi
    
    success "权限检查通过"
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装"
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        error "npm 未安装"
    fi
    
    # 检查 nginx (可选)
    if ! command -v nginx &> /dev/null; then
        warning "nginx 未安装，跳过 nginx 配置"
    fi
    
    success "依赖检查完成"
}

# 创建备份
create_backup() {
    log "创建备份..."
    
    if [ -d "$SERVER_PATH" ]; then
        mkdir -p "$BACKUP_PATH"
        BACKUP_FILE="$BACKUP_PATH/backup_${TIMESTAMP}.tar.gz"
        tar -czf "$BACKUP_FILE" -C "$(dirname "$SERVER_PATH")" "$(basename "$SERVER_PATH")"
        success "备份已创建: $BACKUP_FILE"
    else
        info "服务器路径不存在，跳过备份"
    fi
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    npm ci
    success "依赖安装完成"
}

# 运行测试
run_tests() {
    log "运行测试..."
    
    if npm run test --if-present; then
        success "测试通过"
    else
        warning "测试失败或未配置测试"
        read -p "是否继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            error "部署已取消"
        fi
    fi
}

# 构建项目
build_project() {
    log "构建项目..."
    
    # 清理之前的构建
    rm -rf dist/
    
    # 执行构建
    $BUILD_COMMAND
    
    if [ ! -d "dist" ]; then
        error "构建失败，dist 目录不存在"
    fi
    
    success "项目构建完成"
}

# 部署文件
deploy_files() {
    log "部署文件到服务器..."
    
    # 创建服务器目录
    mkdir -p "$SERVER_PATH"
    
    # 清理旧文件
    rm -rf "${SERVER_PATH:?}"/*
    
    # 复制新文件
    cp -r dist/* "$SERVER_PATH/"
    
    # 设置权限
    chown -R www-data:www-data "$SERVER_PATH" 2>/dev/null || true
    chmod -R 755 "$SERVER_PATH"
    
    success "文件部署完成"
}

# 配置 Nginx
configure_nginx() {
    if command -v nginx &> /dev/null; then
        log "配置 Nginx..."
        
        # 创建 Nginx 配置文件
        cat > "$NGINX_CONFIG" << EOF
server {
    listen 80;
    server_name ${ENVIRONMENT}.spider-admin.local;
    root $SERVER_PATH;
    index index.html;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA 路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF
        
        # 启用站点
        ln -sf "$NGINX_CONFIG" "/etc/nginx/sites-enabled/$(basename "$NGINX_CONFIG")"
        
        # 测试配置
        if nginx -t; then
            systemctl reload nginx
            success "Nginx 配置完成"
        else
            warning "Nginx 配置测试失败"
        fi
    fi
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 检查文件是否存在
    if [ ! -f "$SERVER_PATH/index.html" ]; then
        error "index.html 文件不存在"
    fi
    
    # 检查文件权限
    if [ ! -r "$SERVER_PATH/index.html" ]; then
        error "index.html 文件不可读"
    fi
    
    success "健康检查通过"
}

# 清理函数
cleanup() {
    log "清理临时文件..."
    # 这里可以添加清理逻辑
    success "清理完成"
}

# 显示部署信息
show_deploy_info() {
    echo
    echo -e "${PURPLE}🎉 部署完成！${NC}"
    echo -e "${CYAN}📋 部署信息:${NC}"
    echo -e "  环境: $ENVIRONMENT"
    echo -e "  路径: $SERVER_PATH"
    echo -e "  时间: $(date)"
    echo -e "  备份: $BACKUP_PATH/backup_${TIMESTAMP}.tar.gz"
    echo
    echo -e "${CYAN}🌐 访问地址:${NC}"
    echo -e "  本地: file://$SERVER_PATH/index.html"
    if command -v nginx &> /dev/null; then
        echo -e "  Web: http://${ENVIRONMENT}.spider-admin.local"
    fi
    echo
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 开始部署 Spider Admin 到 $ENVIRONMENT 环境${NC}"
    echo
    
    # 执行部署步骤
    check_permissions
    check_dependencies
    create_backup
    install_dependencies
    run_tests
    build_project
    deploy_files
    configure_nginx
    health_check
    cleanup
    
    show_deploy_info
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志"' ERR

# 执行主函数
main "$@"
