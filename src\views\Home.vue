<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h2 class="welcome-title">
        <el-icon><Star /></el-icon>
        欢迎使用 Spider Admin 管理平台
      </h2>
      <p class="welcome-description">
        统一管理多个服务系统，提供便捷的服务访问和管理功能
      </p>
    </div>

    <!-- 服务卡片网格 -->
    <div class="services-grid">
      <el-card
        v-for="service in appStore.services"
        :key="service.id"
        class="service-card"
        shadow="hover"
        @click="enterService(service)"
      >
        <template #header>
          <div class="card-header">
            <div class="service-icon" :style="{ backgroundColor: service.color }">
              <el-icon size="24">
                <component :is="service.icon" />
              </el-icon>
            </div>
            <span class="service-name">{{ service.name }}</span>
          </div>
        </template>
        
        <div class="card-content">
          <p class="service-description">{{ service.description }}</p>
          
          <div class="service-info">
            <el-tag size="small" type="info">
              端口: {{ service.port }}
            </el-tag>
            <el-tag size="small" :type="isServiceOnline(service) ? 'success' : 'danger'">
              {{ isServiceOnline(service) ? '在线' : '离线' }}
            </el-tag>
            <el-tag size="small" type="warning">
              {{ service.features.length }} 个功能
            </el-tag>
          </div>
          
          <div class="service-actions">
            <el-button 
              type="primary" 
              size="small"
              @click.stop="openService(service)"
            >
              <el-icon><Link /></el-icon>
              直接访问
            </el-button>
            <el-button 
              type="success" 
              size="small"
              @click.stop="enterService(service)"
            >
              <el-icon><Right /></el-icon>
              进入管理
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 系统信息 -->
    <div class="system-info">
      <el-card>
        <template #header>
          <div class="info-header">
            <el-icon><InfoFilled /></el-icon>
            <span>系统信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">当前环境:</span>
              <el-tag :type="isDevelopment ? 'warning' : 'success'">
                {{ isDevelopment ? '开发环境' : '生产环境' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">服务总数:</span>
              <span class="info-value">{{ appStore.services.length }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">功能总数:</span>
              <span class="info-value">{{ totalFeatures }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/stores/app'
import type { ServiceConfig } from '@/config/services'

const router = useRouter()
const appStore = useAppStore()

// 计算属性
const totalFeatures = computed(() => {
  return appStore.services.reduce((total, service) => total + service.features.length, 0)
})

const isDevelopment = computed(() => {
  return import.meta.env.MODE === 'development'
})

// 模拟服务在线状态检查
const isServiceOnline = (service: ServiceConfig) => {
  // 这里可以实现真实的服务状态检查
  // 目前返回随机状态作为演示
  return Math.random() > 0.3
}

// 直接打开服务
const openService = (service: ServiceConfig) => {
  window.open(service.url, '_blank')
  ElMessage.success(`正在打开 ${service.name}`)
}

// 进入服务管理页面
const enterService = (service: ServiceConfig) => {
  appStore.setCurrentService(service)
  router.push(`/service/${service.id}`)
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.welcome-title {
  font-size: 32px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.welcome-description {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.service-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.service-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.service-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-content {
  padding-top: 16px;
}

.service-description {
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.6;
}

.service-info {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
}

.service-actions {
  display: flex;
  gap: 12px;
}

.system-info {
  margin-top: 40px;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.info-label {
  color: #909399;
  font-weight: 500;
}

.info-value {
  color: #303133;
  font-weight: 600;
}

@media (max-width: 768px) {
  .welcome-title {
    font-size: 24px;
  }
  
  .welcome-description {
    font-size: 16px;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .service-actions {
    flex-direction: column;
  }
}
</style>
