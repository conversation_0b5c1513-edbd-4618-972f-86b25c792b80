<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :style="{ width: appStore.sidebarWidth }">
      <SidebarMenu />
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <TopNavbar />
      </el-header>
      
      <!-- 内容区域 -->
      <el-main class="content-area">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import SidebarMenu from '@/components/SidebarMenu.vue'
import TopNavbar from '@/components/TopNavbar.vue'

const appStore = useAppStore()
</script>

<style scoped>
.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  height: 60px !important;
  line-height: 60px;
}
</style>
