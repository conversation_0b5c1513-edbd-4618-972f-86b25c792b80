#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 部署配置
const DEPLOY_CONFIG = {
  development: {
    name: '开发环境',
    buildCommand: 'npm run build:dev',
    outputDir: 'dist',
    serverPath: '/var/www/spider-admin-dev',
    backupDir: '/var/backups/spider-admin-dev'
  },
  production: {
    name: '生产环境',
    buildCommand: 'npm run build',
    outputDir: 'dist',
    serverPath: '/var/www/spider-admin',
    backupDir: '/var/backups/spider-admin'
  }
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function execCommand(command, description) {
  try {
    log(`\n📋 ${description}...`, 'blue')
    execSync(command, { stdio: 'inherit' })
    log(`✅ ${description} 完成`, 'green')
  } catch (error) {
    log(`❌ ${description} 失败: ${error.message}`, 'red')
    process.exit(1)
  }
}

function createBackup(config) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const backupPath = `${config.backupDir}/backup-${timestamp}`
  
  if (fs.existsSync(config.serverPath)) {
    execCommand(
      `mkdir -p ${config.backupDir} && cp -r ${config.serverPath} ${backupPath}`,
      '创建备份'
    )
    log(`📦 备份已保存到: ${backupPath}`, 'cyan')
  }
}

function deploy(environment = 'production') {
  const config = DEPLOY_CONFIG[environment]
  
  if (!config) {
    log(`❌ 未知的环境: ${environment}`, 'red')
    log('可用环境: development, production', 'yellow')
    process.exit(1)
  }

  log(`\n🚀 开始部署到 ${config.name}`, 'magenta')
  log(`📁 输出目录: ${config.outputDir}`, 'cyan')
  log(`🎯 服务器路径: ${config.serverPath}`, 'cyan')

  // 1. 检查依赖
  execCommand('npm ci', '安装依赖')

  // 2. 运行测试
  if (fs.existsSync('package.json')) {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    if (packageJson.scripts && packageJson.scripts.test) {
      execCommand('npm test', '运行测试')
    }
  }

  // 3. 构建项目
  execCommand(config.buildCommand, '构建项目')

  // 4. 检查构建结果
  if (!fs.existsSync(config.outputDir)) {
    log(`❌ 构建输出目录不存在: ${config.outputDir}`, 'red')
    process.exit(1)
  }

  // 5. 创建备份
  createBackup(config)

  // 6. 部署文件
  execCommand(
    `mkdir -p ${config.serverPath} && rm -rf ${config.serverPath}/* && cp -r ${config.outputDir}/* ${config.serverPath}/`,
    '部署文件'
  )

  // 7. 设置权限
  execCommand(
    `chown -R www-data:www-data ${config.serverPath} && chmod -R 755 ${config.serverPath}`,
    '设置权限'
  )

  log(`\n🎉 部署完成！`, 'green')
  log(`🌐 访问地址: http://your-domain.com`, 'cyan')
}

function rollback(environment = 'production') {
  const config = DEPLOY_CONFIG[environment]
  
  if (!config) {
    log(`❌ 未知的环境: ${environment}`, 'red')
    process.exit(1)
  }

  if (!fs.existsSync(config.backupDir)) {
    log(`❌ 备份目录不存在: ${config.backupDir}`, 'red')
    process.exit(1)
  }

  // 获取最新备份
  const backups = fs.readdirSync(config.backupDir)
    .filter(name => name.startsWith('backup-'))
    .sort()
    .reverse()

  if (backups.length === 0) {
    log(`❌ 没有找到备份文件`, 'red')
    process.exit(1)
  }

  const latestBackup = backups[0]
  const backupPath = path.join(config.backupDir, latestBackup)

  log(`\n🔄 开始回滚到 ${config.name}`, 'magenta')
  log(`📦 使用备份: ${latestBackup}`, 'cyan')

  execCommand(
    `rm -rf ${config.serverPath}/* && cp -r ${backupPath}/* ${config.serverPath}/`,
    '恢复备份'
  )

  log(`\n✅ 回滚完成！`, 'green')
}

function showHelp() {
  log('\n📖 Spider Admin 部署脚本', 'magenta')
  log('\n用法:', 'cyan')
  log('  node scripts/deploy.js [命令] [环境]', 'white')
  log('\n命令:', 'cyan')
  log('  deploy    部署应用 (默认)', 'white')
  log('  rollback  回滚到上一个版本', 'white')
  log('  help      显示帮助信息', 'white')
  log('\n环境:', 'cyan')
  log('  development  开发环境', 'white')
  log('  production   生产环境 (默认)', 'white')
  log('\n示例:', 'cyan')
  log('  node scripts/deploy.js deploy production', 'white')
  log('  node scripts/deploy.js rollback development', 'white')
  log('')
}

// 主程序
function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'deploy'
  const environment = args[1] || 'production'

  switch (command) {
    case 'deploy':
      deploy(environment)
      break
    case 'rollback':
      rollback(environment)
      break
    case 'help':
    case '--help':
    case '-h':
      showHelp()
      break
    default:
      log(`❌ 未知命令: ${command}`, 'red')
      showHelp()
      process.exit(1)
  }
}

// 检查是否为直接运行
if (require.main === module) {
  main()
}

module.exports = { deploy, rollback }
